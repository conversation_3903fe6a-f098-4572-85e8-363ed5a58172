#!/bin/bash

# 商品管理系统部署脚本
echo "=========================================="
echo "开始部署商品管理系统到生产环境"
echo "=========================================="

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "请使用root用户运行此脚本"
    exit 1
fi

# 设置变量
PROJECT_DIR="/opt/pms"
JAR_NAME="product-management-system-1.0-SNAPSHOT.jar"
FRONTEND_DIR="/opt/pms/frontend"

echo "1. 检查项目目录..."
if [ ! -d "$PROJECT_DIR" ]; then
    echo "项目目录不存在，正在创建..."
    mkdir -p $PROJECT_DIR
    mkdir -p $PROJECT_DIR/uploads/product_image
    mkdir -p $PROJECT_DIR/logs
    mkdir -p $PROJECT_DIR/backup
    mkdir -p $FRONTEND_DIR
    chmod 755 $PROJECT_DIR
    chmod 755 $PROJECT_DIR/uploads
    chmod 755 $PROJECT_DIR/uploads/product_image
    chmod 755 $PROJECT_DIR/logs
    chmod 755 $PROJECT_DIR/backup
    chmod 755 $FRONTEND_DIR
fi

echo "2. 停止现有服务（如果正在运行）..."
systemctl stop pms-backend 2>/dev/null || true

echo "3. 备份现有文件（如果存在）..."
if [ -f "$PROJECT_DIR/$JAR_NAME" ]; then
    cp "$PROJECT_DIR/$JAR_NAME" "$PROJECT_DIR/backup/$JAR_NAME.$(date +%Y%m%d_%H%M%S)"
    echo "已备份现有jar文件"
fi

if [ -d "$FRONTEND_DIR" ] && [ "$(ls -A $FRONTEND_DIR)" ]; then
    tar -czf "$PROJECT_DIR/backup/frontend_$(date +%Y%m%d_%H%M%S).tar.gz" -C "$FRONTEND_DIR" .
    echo "已备份现有前端文件"
fi

echo "4. 检查必要文件..."
if [ ! -f "./target/$JAR_NAME" ]; then
    echo "错误：找不到后端jar文件 ./target/$JAR_NAME"
    echo "请先在项目根目录运行: mvn clean package -DskipTests"
    exit 1
fi

if [ ! -d "./vue-product/dist" ]; then
    echo "错误：找不到前端构建文件 ./vue-product/dist"
    echo "请先在vue-product目录运行: npm run build"
    exit 1
fi

echo "5. 部署后端文件..."
cp "./target/$JAR_NAME" "$PROJECT_DIR/"
chown root:root "$PROJECT_DIR/$JAR_NAME"
chmod 644 "$PROJECT_DIR/$JAR_NAME"

echo "6. 部署前端文件..."
rm -rf "$FRONTEND_DIR"/*
cp -r "./vue-product/dist"/* "$FRONTEND_DIR/"
chown -R root:root "$FRONTEND_DIR"
find "$FRONTEND_DIR" -type f -exec chmod 644 {} \;
find "$FRONTEND_DIR" -type d -exec chmod 755 {} \;

echo "7. 检查服务配置..."
if [ ! -f "/etc/systemd/system/pms-backend.service" ]; then
    echo "警告：系统服务文件不存在，请手动创建"
fi

echo "8. 启动服务..."
systemctl daemon-reload
systemctl start pms-backend
systemctl enable pms-backend

echo "9. 等待服务启动..."
sleep 5

echo "10. 检查服务状态..."
if systemctl is-active --quiet pms-backend; then
    echo "✅ 后端服务启动成功"
else
    echo "❌ 后端服务启动失败，请检查日志："
    echo "journalctl -u pms-backend -f"
    exit 1
fi

echo "11. 重启Nginx..."
if systemctl is-active --quiet nginx; then
    systemctl reload nginx
    echo "✅ Nginx配置已重新加载"
else
    echo "⚠️  Nginx未运行，请手动启动：systemctl start nginx"
fi

echo "=========================================="
echo "部署完成！"
echo "=========================================="
echo "访问地址："
echo "前端页面: http://117.72.102.37"
echo "后端API: http://117.72.102.37:8082"
echo ""
echo "检查命令："
echo "查看后端日志: journalctl -u pms-backend -f"
echo "查看服务状态: systemctl status pms-backend"
echo "查看Nginx状态: systemctl status nginx"
echo "=========================================="
