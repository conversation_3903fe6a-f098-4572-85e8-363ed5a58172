# 商品管理系统 CentOS7 部署指南

## 概述

本指南将帮助您将商品管理系统部署到CentOS7云主机上，包括图片存储路径的配置和环境适配。

## 修改内容总结

### 1. 后端修改

#### 配置文件更改
- **新增**: `FileUploadProperties.java` - 文件上传配置属性类
- **修改**: `FileUploadController.java` - 使用配置化的存储路径
- **修改**: `WebConfig.java` - 动态配置静态资源映射
- **新增**: `application-prod.yml` - 生产环境配置文件

#### 关键配置项
```yaml
# 生产环境文件上传配置
file:
  upload:
    path: /opt/pms/uploads/product_image/  # Linux路径
    url-prefix: /product_image
    allowed-extensions: .jpg,.jpeg,.png,.gif,.bmp
    max-size: 5242880  # 5MB
```

### 2. 前端修改

#### API配置更改
- **修改**: `src/api/product.js` - 动态API基础URL
- **修改**: `src/components/ProductManagement.vue` - 动态上传地址和图片URL
- **修改**: `src/views/Mall.vue` - 动态图片URL处理

#### 环境适配逻辑
```javascript
// 根据环境动态设置API地址
const getBaseURL = () => {
  if (process.env.NODE_ENV === 'development') {
    return 'http://localhost:8082/api'
  }
  return 'http://*************:8082/api'  // 云主机IP
}
```

## 部署步骤

### 第一步：准备云主机环境

1. **运行部署脚本**
```bash
chmod +x deploy-centos7.sh
sudo ./deploy-centos7.sh
```

2. **手动安装MySQL 8.0**（如果未安装）
```bash
# 下载MySQL仓库
wget https://dev.mysql.com/get/mysql80-community-release-el7-3.noarch.rpm
sudo rpm -ivh mysql80-community-release-el7-3.noarch.rpm

# 安装MySQL
sudo yum install -y mysql-server

# 启动MySQL服务
sudo systemctl start mysqld
sudo systemctl enable mysqld

# 获取临时密码
sudo grep 'temporary password' /var/log/mysqld.log

# 安全配置
sudo mysql_secure_installation
```

### 第二步：配置IP地址

**已完成**: 代码中的IP地址已更新为您的云主机IP `*************`

已修改的文件：
1. `vue-product/src/api/product.js` ✅
2. `vue-product/src/api/user.js` ✅
3. `vue-product/src/api/admin.js` ✅
4. `vue-product/src/api/category.js` ✅
5. `vue-product/src/api/stock.js` ✅
6. `vue-product/src/components/ProductManagement.vue` ✅
7. `vue-product/src/views/Mall.vue` ✅
8. `product-management-system/src/main/resources/application-prod.yml` ✅

### 第三步：构建和部署

1. **构建后端**
```bash
cd product-management-system
mvn clean package -DskipTests
```

2. **构建前端**
```bash
cd vue-product
npm install
npm run build
```

3. **复制文件到服务器**
```bash
# 复制后端jar包
sudo cp product-management-system/target/product-management-system-1.0-SNAPSHOT.jar /opt/pms/

# 复制前端构建文件
sudo mkdir -p /opt/pms/frontend
sudo cp -r vue-product/dist/* /opt/pms/frontend/
```

### 第四步：初始化数据库

```bash
# 创建数据库和表
mysql -u root -p < /opt/pms/init-database.sql

# 导入测试数据（可选）
mysql -u root -p pms < vue-product/insert_mall_data.sql
```

### 第五步：启动服务

```bash
# 启动后端服务
sudo systemctl start pms-backend
sudo systemctl enable pms-backend

# 检查服务状态
sudo systemctl status pms-backend

# 查看日志
sudo journalctl -u pms-backend -f
```

### 第六步：配置Nginx（可选）

```bash
# 安装Nginx
sudo yum install -y nginx

# 启动Nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# 检查配置
sudo nginx -t

# 重新加载配置
sudo systemctl reload nginx
```

## 目录结构

部署后的目录结构：
```
/opt/pms/
├── product-management-system-1.0-SNAPSHOT.jar  # 后端应用
├── frontend/                                    # 前端静态文件
│   ├── index.html
│   ├── static/
│   └── ...
├── uploads/                                     # 文件上传目录
│   └── product_image/                          # 商品图片存储
├── logs/                                       # 日志目录
│   └── application.log
├── backup/                                     # 备份目录
└── init-database.sql                          # 数据库初始化脚本
```

## 访问地址

- **前端页面**: `http://*************`
- **后端API**: `http://*************:8082`
- **API文档**: `http://*************:8082/swagger-ui.html`

## 常见问题

### 1. 图片上传失败
- 检查目录权限：`sudo chmod 755 /opt/pms/uploads/product_image/`
- 检查磁盘空间：`df -h`

### 2. 服务启动失败
- 查看日志：`sudo journalctl -u pms-backend -f`
- 检查端口占用：`sudo netstat -tlnp | grep 8082`

### 3. 数据库连接失败
- 检查MySQL服务：`sudo systemctl status mysqld`
- 验证数据库配置：检查用户名、密码、数据库名

### 4. 防火墙问题
```bash
# 检查防火墙状态
sudo firewall-cmd --state

# 开放端口
sudo firewall-cmd --permanent --add-port=8082/tcp
sudo firewall-cmd --reload
```

## 安全建议

1. **修改默认密码**: 更改数据库root密码和应用配置
2. **配置SSL**: 使用HTTPS访问（推荐使用Let's Encrypt）
3. **限制访问**: 配置防火墙规则，只开放必要端口
4. **定期备份**: 设置数据库和文件的定期备份

## 监控和维护

1. **日志监控**
```bash
# 查看应用日志
tail -f /opt/pms/logs/application.log

# 查看系统日志
sudo journalctl -u pms-backend -f
```

2. **性能监控**
```bash
# 查看系统资源
top
htop
free -h
df -h
```

3. **服务管理**
```bash
# 重启服务
sudo systemctl restart pms-backend

# 停止服务
sudo systemctl stop pms-backend

# 查看服务状态
sudo systemctl status pms-backend
```

## 联系支持

如果在部署过程中遇到问题，请检查：
1. 系统日志
2. 应用日志
3. 网络连接
4. 防火墙配置
5. 文件权限
