server {
    listen 80;
    server_name *************;  # 您的云主机IP

    # 设置客户端最大请求体大小（用于文件上传）
    client_max_body_size 10M;

    # 前端静态文件
    location / {
        root /opt/pms/frontend;
        try_files $uri $uri/ /index.html;
        index index.html;
        
        # 静态资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # 后端API代理
    location /api/ {
        proxy_pass http://localhost:8082/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 商品图片静态资源
    location /product_image/ {
        proxy_pass http://localhost:8082/product_image/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        # 图片缓存
        expires 30d;
        add_header Cache-Control "public";
    }

    # 错误页面
    error_page 404 /index.html;
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }

    # 日志配置
    access_log /var/log/nginx/pms_access.log;
    error_log /var/log/nginx/pms_error.log;
}
