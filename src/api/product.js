import axios from 'axios'

// 根据环境变量确定API基础URL
const getBaseURL = () => {
  // 开发环境
  if (process.env.NODE_ENV === 'development') {
    return 'http://localhost:8082/api'
  }
  // 生产环境 - 云主机IP
  return 'http://*************:8082/api'
}

// 创建axios实例
const api = axios.create({
  baseURL: getBaseURL(),
  timeout: 30000, // 增加超时时间，因为图片上传可能需要更长时间
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    console.log('商品API请求:', config.method?.toUpperCase(), config.url, config.data || config.params)
    return config
  },
  error => {
    console.error('商品API请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    console.log('商品API响应:', response.config.url, response.data)
    return response
  },
  error => {
    console.error('商品API响应错误:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

// 商品管理API
export const productAPI = {
  /**
   * 获取所有商品列表（包含分类和库存信息）- 管理员使用
   */
  getAllProducts() {
    return api.get('/product/list')
  },

  /**
   * 获取上架商品列表（包含分类信息）- 普通用户使用
   */
  getOnlineProducts() {
    return api.get('/product/online')
  },

  /**
   * 获取商品详情
   * @param {number} productId - 商品ID
   */
  getProductDetail(productId) {
    return api.get(`/product/${productId}`)
  },

  /**
   * 添加商品
   * @param {Object} productData - 商品数据
   * @param {string} productData.name - 商品名称
   * @param {number} productData.categoryId - 分类ID
   * @param {number} productData.price - 商品价格
   * @param {string} productData.description - 商品描述
   * @param {string} productData.imageUrl - 商品图片URL
   */
  addProduct(productData) {
    return api.post('/product/add', productData)
  },

  /**
   * 更新商品信息
   * @param {Object} productData - 商品数据
   * @param {number} productData.id - 商品ID
   * @param {string} productData.name - 商品名称
   * @param {number} productData.categoryId - 分类ID
   * @param {number} productData.price - 商品价格
   * @param {string} productData.description - 商品描述
   * @param {string} productData.imageUrl - 商品图片URL
   */
  updateProduct(productData) {
    return api.put('/product/update', productData)
  },

  /**
   * 删除商品
   * @param {number} productId - 商品ID
   */
  deleteProduct(productId) {
    return api.delete(`/product/${productId}`)
  },

  /**
   * 上架商品
   * @param {number} productId - 商品ID
   */
  onlineProduct(productId) {
    return api.put(`/product/online/${productId}`)
  },

  /**
   * 下架商品
   * @param {number} productId - 商品ID
   */
  offlineProduct(productId) {
    return api.put(`/product/offline/${productId}`)
  },

  /**
   * 根据分类获取商品列表
   * @param {number} categoryId - 分类ID
   */
  getProductsByCategory(categoryId) {
    return api.get(`/product/category/${categoryId}`)
  },

  /**
   * 搜索商品
   * @param {string} keyword - 搜索关键词
   */
  searchProducts(keyword) {
    return api.get(`/product/search`, {
      params: { keyword }
    })
  }
}

// 文件上传API
export const uploadAPI = {
  /**
   * 上传商品图片
   * @param {File} file - 图片文件
   */
  uploadProductImage(file) {
    const formData = new FormData()
    formData.append('file', file)

    return axios.post(getBaseURL() + '/upload/product-image', formData, {
      headers: {
        // 不要手动设置Content-Type，让浏览器自动设置boundary
      },
      timeout: 30000
    })
  },

  /**
   * 删除商品图片
   * @param {string} fileName - 文件名
   */
  deleteProductImage(fileName) {
    return api.delete('/upload/product-image', {
      params: { fileName }
    })
  }
}

export default { productAPI, uploadAPI }
