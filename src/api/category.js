import axios from 'axios'

// 根据环境变量确定API基础URL
const getBaseURL = () => {
  // 开发环境
  if (process.env.NODE_ENV === 'development') {
    return 'http://localhost:8082/api'
  }
  // 生产环境 - 云主机IP
  return 'http://*************:8082/api'
}

// 创建axios实例
const api = axios.create({
  baseURL: getBaseURL(),
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    console.log('分类API请求:', config.method?.toUpperCase(), config.url, config.data || config.params)
    return config
  },
  error => {
    console.error('分类API请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    console.log('分类API响应:', response.config.url, response.data)
    return response
  },
  error => {
    console.error('分类API响应错误:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

// 分类管理API
export const categoryAPI = {
  /**
   * 获取所有分类列表
   */
  getAllCategories() {
    return api.get('/product/categories')
  },

  /**
   * 添加分类
   * @param {Object} categoryData - 分类数据
   * @param {string} categoryData.name - 分类名称
   */
  addCategory(categoryData) {
    return api.post('/product/category', categoryData)
  },

  /**
   * 更新分类
   * @param {Object} categoryData - 分类数据
   * @param {number} categoryData.id - 分类ID
   * @param {string} categoryData.name - 分类名称
   */
  updateCategory(categoryData) {
    return api.put('/product/category', categoryData)
  },

  /**
   * 删除分类
   * @param {number} categoryId - 分类ID
   */
  deleteCategory(categoryId) {
    return api.delete(`/product/category/${categoryId}`)
  },

  /**
   * 获取分类详情（包含商品数量）
   * @param {number} categoryId - 分类ID
   */
  getCategoryDetail(categoryId) {
    return api.get(`/product/category/${categoryId}/detail`)
  },

  /**
   * 根据分类获取商品列表
   * @param {number} categoryId - 分类ID
   */
  getProductsByCategory(categoryId) {
    return api.get(`/product/category/${categoryId}`)
  }
}

export default categoryAPI
