<template>
  <div class="product-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>商品管理</h2>
        <p class="header-desc">管理商品信息，支持添加、编辑、上架、下架操作</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showAddDialog" :icon="Plus">
          添加商品
        </el-button>
        <el-button @click="loadProducts" :icon="Refresh" :loading="loading">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-section">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索商品名称"
            clearable
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          >
            <template #append>
              <el-button @click="handleSearch" :icon="Search">搜索</el-button>
            </template>
          </el-input>
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="selectedCategory"
            placeholder="选择分类"
            clearable
            @change="handleCategoryFilter"
            style="width: 100%"
          >
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="selectedStatus"
            placeholder="选择状态"
            clearable
            @change="handleStatusFilter"
            style="width: 100%"
          >
            <el-option label="上架" :value="1" />
            <el-option label="下架" :value="0" />
          </el-select>
        </el-col>
      </el-row>
    </div>

    <!-- 商品列表 -->
    <div class="product-list">
      <el-table 
        :data="filteredProducts" 
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        empty-text="暂无商品数据"
      >
        <el-table-column prop="id" label="商品ID" width="80" align="center" />
        
        <el-table-column label="商品图片" width="100" align="center">
          <template #default="{ row }">
            <div class="product-image">
              <el-image
                v-if="row.imageUrl && row.imageUrl.trim() !== ''"
                :src="getImageUrl(row.imageUrl)"
                :preview-src-list="[getImageUrl(row.imageUrl)]"
                fit="cover"
                style="width: 60px; height: 60px; border-radius: 6px;"
                :preview-teleported="true"
                @error="handleImageError"
              />
              <div v-else class="no-image">
                <el-icon><Picture /></el-icon>
                <span>暂无图片</span>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="name" label="商品名称" min-width="150">
          <template #default="{ row }">
            <div class="product-name">
              <span class="name-text">{{ row.name }}</span>
              <el-tag v-if="row.categoryName" size="small" type="info">
                {{ row.categoryName }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="price" label="价格" width="100" align="center">
          <template #default="{ row }">
            <span class="price-text">¥{{ row.price }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="库存" width="120" align="center">
          <template #default="{ row }">
            <div class="stock-info">
              <el-tag
                :type="getStockTagType(row.quantity)"
                size="small"
                class="stock-tag"
              >
                {{ row.quantity || 0 }}
              </el-tag>
              <div class="stock-status">
                {{ getStockStatusText(row.quantity, row.min_stock) }}
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag 
              :type="row.status === 1 ? 'success' : 'danger'"
              size="small"
            >
              {{ row.status === 1 ? '上架' : '下架' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="createTime" label="创建时间" width="160" align="center">
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="280" align="center" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="showEditDialog(row)"
              :icon="Edit"
            >
              编辑
            </el-button>

            <el-button
              type="info"
              size="small"
              @click="showStockDialog(row)"
              plain
            >
              库存
            </el-button>

            <el-button
              v-if="row.status === 0"
              type="success"
              size="small"
              @click="handleOnline(row)"
              :icon="Top"
            >
              上架
            </el-button>
            
            <el-button 
              v-if="row.status === 1"
              type="warning" 
              size="small" 
              @click="handleOffline(row)"
              :icon="Bottom"
            >
              下架
            </el-button>
            
            <el-button 
              type="danger" 
              size="small" 
              @click="handleDelete(row)"
              :icon="Delete"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 添加/编辑商品对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="800px"
      :close-on-click-modal="false"
      @close="resetForm"
    >
      <el-form
        ref="productFormRef"
        :model="productForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="商品名称" prop="name">
              <el-input
                v-model="productForm.name"
                placeholder="请输入商品名称"
                maxlength="100"
                show-word-limit
                clearable
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="商品分类" prop="categoryId">
              <el-select
                v-model="productForm.categoryId"
                placeholder="请选择商品分类"
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="category in categories"
                  :key="category.id"
                  :label="category.name"
                  :value="category.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="商品价格" prop="price">
              <el-input-number
                v-model="productForm.price"
                :min="0.01"
                :max="999999.99"
                :precision="2"
                :step="0.01"
                placeholder="请输入商品价格"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="商品状态" prop="status">
              <el-radio-group v-model="productForm.status">
                <el-radio :label="1">上架</el-radio>
                <el-radio :label="0">下架</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="库存数量" prop="quantity">
              <el-input-number
                v-model="productForm.quantity"
                :min="0"
                :max="999999"
                :step="1"
                placeholder="请输入库存数量"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="最低库存" prop="minStock">
              <el-input-number
                v-model="productForm.minStock"
                :min="0"
                :max="999999"
                :step="1"
                placeholder="请输入最低库存预警值"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="商品图片" prop="imageUrl">
          <div class="image-upload-section">
            <el-upload
              ref="uploadRef"
              :action="uploadAction"
              :headers="uploadHeaders"
              :show-file-list="false"
              :before-upload="beforeImageUpload"
              :on-success="handleImageSuccess"
              :on-error="handleImageError"
              accept="image/*"
              drag
            >
              <div v-if="!productForm.imageUrl" class="upload-placeholder">
                <el-icon class="upload-icon"><Plus /></el-icon>
                <div class="upload-text">
                  <p>点击或拖拽图片到此处上传</p>
                  <p class="upload-tip">支持 jpg、png、gif 格式，文件大小不超过 5MB</p>
                </div>
              </div>
              
              <div v-else class="uploaded-image">
                <el-image
                  :src="getImageUrl(productForm.imageUrl)"
                  fit="cover"
                  style="width: 200px; height: 200px; border-radius: 8px;"
                />
                <div class="image-overlay">
                  <el-button 
                    type="danger" 
                    size="small" 
                    @click.stop="removeImage"
                    :icon="Delete"
                  >
                    删除图片
                  </el-button>
                </div>
              </div>
            </el-upload>
          </div>
        </el-form-item>
        
        <el-form-item label="商品描述" prop="description">
          <el-input
            v-model="productForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入商品描述"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleSubmit"
            :loading="submitting"
          >
            {{ isEdit ? '更新' : '添加' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 库存管理对话框 -->
    <el-dialog
      title="库存管理"
      v-model="stockDialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="stock-management">
        <div class="product-info">
          <h4>{{ currentProduct.name }}</h4>
          <p>商品ID: {{ currentProduct.id }}</p>
        </div>

        <el-form
          ref="stockFormRef"
          :model="stockForm"
          :rules="stockFormRules"
          label-width="120px"
        >
          <el-form-item label="当前库存" prop="currentStock">
            <el-input-number
              v-model="stockForm.currentStock"
              :min="0"
              :max="999999"
              :step="1"
              style="width: 200px"
              disabled
            />
            <span class="stock-status-text">
              {{ getStockStatusText(stockForm.currentStock, stockForm.minStock) }}
            </span>
          </el-form-item>

          <el-form-item label="最低库存预警" prop="minStock">
            <el-input-number
              v-model="stockForm.minStock"
              :min="0"
              :max="999999"
              :step="1"
              style="width: 200px"
            />
          </el-form-item>

          <el-form-item label="库存调整" prop="adjustment">
            <el-radio-group v-model="stockForm.adjustmentType" @change="handleAdjustmentTypeChange">
              <el-radio label="add">增加库存</el-radio>
              <el-radio label="reduce">减少库存</el-radio>
              <el-radio label="set">设置库存</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="调整数量" prop="adjustmentQuantity" v-if="stockForm.adjustmentType">
            <el-input-number
              v-model="stockForm.adjustmentQuantity"
              :min="stockForm.adjustmentType === 'reduce' ? 1 : (stockForm.adjustmentType === 'set' ? 0 : 1)"
              :max="stockForm.adjustmentType === 'reduce' ? stockForm.currentStock : 999999"
              :step="1"
              style="width: 200px"
              :placeholder="getAdjustmentPlaceholder()"
            />
          </el-form-item>

          <el-form-item label="调整原因" prop="reason">
            <el-input
              v-model="stockForm.reason"
              type="textarea"
              :rows="3"
              placeholder="请输入库存调整原因"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="stockDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleStockSubmit"
            :loading="stockSubmitting"
          >
            确认调整
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  Refresh, 
  Search, 
  Edit, 
  Delete, 
  Top, 
  Bottom, 
  Picture 
} from '@element-plus/icons-vue'
import { productAPI } from '@/api/product'
import { categoryAPI } from '@/api/category'
import { stockAPI } from '@/api/stock'

export default {
  name: 'ProductManagement',
  components: {
    Plus,
    Refresh,
    Search,
    Edit,
    Delete,
    Top,
    Bottom,
    Picture
  },
  setup() {
    // 响应式数据
    const loading = ref(false)
    const submitting = ref(false)
    const products = ref([])
    const categories = ref([])
    const dialogVisible = ref(false)
    const isEdit = ref(false)
    const productFormRef = ref(null)
    const uploadRef = ref(null)

    // 库存管理相关
    const stockDialogVisible = ref(false)
    const stockSubmitting = ref(false)
    const stockFormRef = ref(null)
    const currentProduct = ref({})

    // 搜索和筛选
    const searchKeyword = ref('')
    const selectedCategory = ref(null)
    const selectedStatus = ref(null)
    
    // 表单数据
    const productForm = reactive({
      id: null,
      name: '',
      categoryId: null,
      price: null,
      description: '',
      imageUrl: '',
      status: 1,
      quantity: 0,
      minStock: 10
    })

    // 库存表单数据
    const stockForm = reactive({
      productId: null,
      currentStock: 0,
      minStock: 10,
      adjustmentType: '',
      adjustmentQuantity: null,
      reason: ''
    })

    // 上传配置 - 根据环境动态设置
    const getUploadAction = () => {
      if (process.env.NODE_ENV === 'development') {
        return 'http://localhost:8082/api/upload/product-image'
      }
      // 生产环境 - 云主机IP
      return 'http://*************:8082/api/upload/product-image'
    }
    const uploadAction = getUploadAction()
    const uploadHeaders = {
      // 不要设置Content-Type，让浏览器自动设置multipart boundary
    }
    
    // 表单验证规则
    const formRules = {
      name: [
        { required: true, message: '请输入商品名称', trigger: 'blur' },
        { min: 1, max: 100, message: '商品名称长度在 1 到 100 个字符', trigger: 'blur' }
      ],
      categoryId: [
        { required: true, message: '请选择商品分类', trigger: 'change' }
      ],
      price: [
        { required: true, message: '请输入商品价格', trigger: 'blur' },
        { type: 'number', min: 0.01, message: '商品价格必须大于0', trigger: 'blur' }
      ],
      status: [
        { required: true, message: '请选择商品状态', trigger: 'change' }
      ],
      quantity: [
        { required: true, message: '请输入库存数量', trigger: 'blur' },
        { type: 'number', min: 0, message: '库存数量不能小于0', trigger: 'blur' }
      ],
      minStock: [
        { required: true, message: '请输入最低库存', trigger: 'blur' },
        { type: 'number', min: 0, message: '最低库存不能小于0', trigger: 'blur' }
      ]
    }

    // 库存表单验证规则
    const stockFormRules = {
      minStock: [
        { required: true, message: '请输入最低库存预警值', trigger: 'blur' },
        { type: 'number', min: 0, message: '最低库存不能小于0', trigger: 'blur' }
      ],
      adjustmentQuantity: [
        { required: true, message: '请输入调整数量', trigger: 'blur' },
        { type: 'number', min: 1, message: '调整数量必须大于0', trigger: 'blur' }
      ],
      reason: [
        { required: true, message: '请输入调整原因', trigger: 'blur' },
        { min: 1, max: 200, message: '调整原因长度在 1 到 200 个字符', trigger: 'blur' }
      ]
    }

    // 计算属性
    const dialogTitle = computed(() => {
      return isEdit.value ? '编辑商品' : '添加商品'
    })
    
    const filteredProducts = computed(() => {
      let result = [...products.value]

      console.log('筛选条件:', {
        searchKeyword: searchKeyword.value,
        selectedCategory: selectedCategory.value,
        selectedStatus: selectedStatus.value,
        totalProducts: products.value.length
      })

      // 关键词搜索
      if (searchKeyword.value) {
        result = result.filter(product =>
          product.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
        )
        console.log('关键词搜索后:', result.length)
      }

      // 分类筛选
      if (selectedCategory.value) {
        console.log('分类筛选前商品数量:', result.length)
        console.log('选择的分类ID:', selectedCategory.value, typeof selectedCategory.value)
        console.log('商品分类ID示例:', result.slice(0, 3).map(p => ({
          name: p.name,
          categoryId: p.categoryId,
          category_id: p.category_id,
          categoryIdType: typeof p.categoryId,
          category_idType: typeof p.category_id
        })))

        result = result.filter(product => {
          // 尝试两种字段名和类型转换
          const match1 = product.categoryId == selectedCategory.value
          const match2 = product.category_id == selectedCategory.value
          return match1 || match2
        })
        console.log('分类筛选后商品数量:', result.length)
      }

      // 状态筛选
      if (selectedStatus.value !== null) {
        result = result.filter(product => product.status === selectedStatus.value)
        console.log('状态筛选后商品数量:', result.length)
      }

      console.log('最终筛选结果:', result.length)
      return result
    })
    
    // 加载商品列表
    const loadProducts = async () => {
      loading.value = true
      try {
        const response = await productAPI.getAllProducts()
        if (response.data.success) {
          // 映射后端字段名到前端字段名
          products.value = response.data.data.map(product => ({
            ...product,
            imageUrl: product.image_url,  // 映射 image_url 到 imageUrl
            categoryName: product.category_name,  // 映射 category_name 到 categoryName
            categoryId: product.category_id,  // 映射 category_id 到 categoryId
            quantity: product.quantity || 0,  // 库存数量
            min_stock: product.min_stock || 10  // 最低库存
          }))
          ElMessage.success('商品列表加载成功')
        } else {
          ElMessage.error(response.data.message || '获取商品列表失败')
        }
      } catch (error) {
        console.error('获取商品列表失败:', error)
        ElMessage.error('获取商品列表失败')
      } finally {
        loading.value = false
      }
    }

    // 加载分类列表
    const loadCategories = async () => {
      try {
        const response = await categoryAPI.getAllCategories()
        if (response.data.success) {
          categories.value = response.data.data
        } else {
          ElMessage.error('获取分类列表失败')
        }
      } catch (error) {
        console.error('获取分类列表失败:', error)
        ElMessage.error('获取分类列表失败')
      }
    }

    // 显示添加对话框
    const showAddDialog = () => {
      isEdit.value = false
      dialogVisible.value = true
      resetForm()
    }

    // 显示编辑对话框
    const showEditDialog = (product) => {
      isEdit.value = true
      dialogVisible.value = true
      productForm.id = product.id
      productForm.name = product.name
      productForm.categoryId = product.categoryId
      productForm.price = product.price
      productForm.description = product.description
      productForm.imageUrl = product.imageUrl
      productForm.status = product.status
      productForm.quantity = product.quantity || 0
      productForm.minStock = product.min_stock || 10
    }

    // 重置表单
    const resetForm = () => {
      productForm.id = null
      productForm.name = ''
      productForm.categoryId = null
      productForm.price = null
      productForm.description = ''
      productForm.imageUrl = ''
      productForm.status = 1
      productForm.quantity = 0
      productForm.minStock = 10
      if (productFormRef.value) {
        productFormRef.value.resetFields()
      }
    }

    // 提交表单
    const handleSubmit = async () => {
      if (!productFormRef.value) return

      try {
        await productFormRef.value.validate()
        submitting.value = true

        const formData = {
          name: productForm.name.trim(),
          categoryId: productForm.categoryId,
          price: productForm.price,
          description: productForm.description?.trim() || '',
          imageUrl: productForm.imageUrl || '',
          status: productForm.status,
          quantity: productForm.quantity || 0,
          minStock: productForm.minStock || 10
        }

        if (isEdit.value) {
          formData.id = productForm.id
          const response = await productAPI.updateProduct(formData)

          if (response.data.success) {
            ElMessage.success('商品更新成功')
            dialogVisible.value = false
            loadProducts()
          } else {
            ElMessage.error(response.data.message || '商品更新失败')
          }
        } else {
          const response = await productAPI.addProduct(formData)

          if (response.data.success) {
            ElMessage.success('商品添加成功')
            dialogVisible.value = false
            loadProducts()
          } else {
            ElMessage.error(response.data.message || '商品添加失败')
          }
        }
      } catch (error) {
        if (error !== false) {
          console.error('提交商品失败:', error)
          ElMessage.error('操作失败，请重试')
        }
      } finally {
        submitting.value = false
      }
    }

    // 上架商品
    const handleOnline = async (product) => {
      try {
        const response = await productAPI.onlineProduct(product.id)
        if (response.data.success) {
          ElMessage.success('商品上架成功')
          loadProducts()
        } else {
          ElMessage.error(response.data.message || '商品上架失败')
        }
      } catch (error) {
        console.error('商品上架失败:', error)
        ElMessage.error('商品上架失败')
      }
    }

    // 下架商品
    const handleOffline = async (product) => {
      try {
        const response = await productAPI.offlineProduct(product.id)
        if (response.data.success) {
          ElMessage.success('商品下架成功')
          loadProducts()
        } else {
          ElMessage.error(response.data.message || '商品下架失败')
        }
      } catch (error) {
        console.error('商品下架失败:', error)
        ElMessage.error('商品下架失败')
      }
    }

    // 删除商品
    const handleDelete = async (product) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除商品"${product.name}"吗？此操作不可恢复。`,
          '删除确认',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning',
            confirmButtonClass: 'el-button--danger'
          }
        )

        const response = await productAPI.deleteProduct(product.id)
        if (response.data.success) {
          ElMessage.success('商品删除成功')
          loadProducts()
        } else {
          ElMessage.error(response.data.message || '商品删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除商品失败:', error)
          ElMessage.error('删除失败，请重试')
        }
      }
    }

    // 搜索处理
    const handleSearch = () => {
      // 搜索逻辑已在计算属性中处理
      console.log('搜索关键词:', searchKeyword.value)
    }

    // 分类筛选处理
    const handleCategoryFilter = () => {
      console.log('分类筛选触发:', {
        selectedCategory: selectedCategory.value,
        categoryType: typeof selectedCategory.value,
        availableCategories: categories.value.map(c => ({id: c.id, name: c.name}))
      })
    }

    // 状态筛选处理
    const handleStatusFilter = () => {
      console.log('选择状态:', selectedStatus.value)
    }

    // 图片上传前验证
    const beforeImageUpload = (file) => {
      const isImage = file.type.startsWith('image/')
      const isLt5M = file.size / 1024 / 1024 < 5

      if (!isImage) {
        ElMessage.error('只能上传图片文件!')
        return false
      }
      if (!isLt5M) {
        ElMessage.error('图片大小不能超过 5MB!')
        return false
      }
      return true
    }

    // 图片上传成功
    const handleImageSuccess = (response) => {
      if (response.success) {
        productForm.imageUrl = response.data.imageUrl
        ElMessage.success('图片上传成功')
      } else {
        ElMessage.error(response.message || '图片上传失败')
      }
    }

    // 图片上传失败
    const handleImageError = (error) => {
      console.error('图片上传失败:', error)
      ElMessage.error('图片上传失败，请重试')
    }

    // 删除图片
    const removeImage = () => {
      productForm.imageUrl = ''
      ElMessage.success('图片已删除')
    }

    // 获取图片URL
    const getImageUrl = (imageUrl) => {
      if (!imageUrl || imageUrl.trim() === '') return ''

      // 如果已经是完整URL，直接返回
      if (imageUrl.startsWith('http')) {
        return imageUrl
      }

      // 根据环境动态设置服务器地址
      const serverUrl = process.env.NODE_ENV === 'development'
        ? 'http://localhost:8082'
        : 'http://*************:8082'

      // 如果是相对路径，添加服务器地址
      return `${serverUrl}${imageUrl}`
    }


    // 获取库存标签类型
    const getStockTagType = (quantity) => {
      if (quantity === 0) return 'danger'
      if (quantity < 10) return 'warning'
      return 'success'
    }

    // 获取库存状态文本
    const getStockStatusText = (quantity, minStock = 10) => {
      if (quantity === 0) return '缺货'
      if (quantity <= minStock) return '库存不足'
      return '库存充足'
    }

    // 显示库存管理对话框
    const showStockDialog = (product) => {
      currentProduct.value = { ...product }
      stockForm.productId = product.id
      stockForm.currentStock = product.quantity || 0
      stockForm.minStock = product.min_stock || 10
      stockForm.adjustmentType = ''
      stockForm.adjustmentQuantity = null
      stockForm.reason = ''
      stockDialogVisible.value = true
    }

    // 处理库存调整类型变化
    const handleAdjustmentTypeChange = () => {
      stockForm.adjustmentQuantity = null
    }

    // 获取调整数量占位符
    const getAdjustmentPlaceholder = () => {
      switch (stockForm.adjustmentType) {
        case 'add':
          return '请输入要增加的库存数量'
        case 'reduce':
          return '请输入要减少的库存数量'
        case 'set':
          return '请输入要设置的库存数量'
        default:
          return '请输入数量'
      }
    }

    // 提交库存调整
    const handleStockSubmit = async () => {
      if (!stockFormRef.value) return

      try {
        await stockFormRef.value.validate()
        stockSubmitting.value = true

        // 调用库存调整API
        const adjustmentData = {
          productId: stockForm.productId,
          type: stockForm.adjustmentType,
          quantity: stockForm.adjustmentQuantity,
          reason: stockForm.reason
        }

        const response = await stockAPI.adjustStock(adjustmentData)

        if (response.data.success) {
          ElMessage.success('库存调整成功')
          stockDialogVisible.value = false
          loadProducts() // 重新加载商品列表以更新库存显示
        } else {
          ElMessage.error(response.data.message || '库存调整失败')
        }
      } catch (error) {
        console.error('库存调整失败:', error)
        ElMessage.error('库存调整失败，请重试')
      } finally {
        stockSubmitting.value = false
      }
    }

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    // 组件挂载时执行
    onMounted(() => {
      loadProducts()
      loadCategories()
    })

    return {
      loading,
      submitting,
      products,
      categories,
      dialogVisible,
      isEdit,
      productFormRef,
      uploadRef,
      searchKeyword,
      selectedCategory,
      selectedStatus,
      productForm,
      stockDialogVisible,
      stockSubmitting,
      stockFormRef,
      currentProduct,
      stockForm,
      stockFormRules,
      uploadAction,
      uploadHeaders,
      formRules,
      dialogTitle,
      filteredProducts,
      loadProducts,
      loadCategories,
      showAddDialog,
      showEditDialog,
      resetForm,
      handleSubmit,
      handleOnline,
      handleOffline,
      handleDelete,
      // 搜索和筛选方法
      handleSearch,
      handleCategoryFilter,
      handleStatusFilter,
      // 图片处理方法
      beforeImageUpload,
      handleImageSuccess,
      handleImageError,
      removeImage,
      getImageUrl,
      // 库存管理方法
      showStockDialog,
      handleAdjustmentTypeChange,
      getAdjustmentPlaceholder,
      handleStockSubmit,
      // 工具方法
      getStockTagType,
      getStockStatusText,
      formatDate
    }
  }
}
</script>

<style scoped>
.product-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
}

/* 页面头部样式 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.header-left h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
  font-weight: 600;
}

.header-desc {
  margin: 0;
  color: #909399;
  font-size: 14px;
  line-height: 1.5;
}

.header-right {
  display: flex;
  gap: 12px;
}

/* 搜索区域样式 */
.search-section {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

/* 商品列表样式 */
.product-list {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.product-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.no-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: #f5f7fa;
  border: 1px dashed #dcdfe6;
  border-radius: 6px;
  color: #c0c4cc;
  font-size: 12px;
}

.no-image .el-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.product-name {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.name-text {
  font-weight: 500;
  color: #303133;
}

.price-text {
  font-weight: 600;
  color: #f56c6c;
  font-size: 16px;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 0;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 16px 0;
}

:deep(.el-table .el-button) {
  margin: 0 2px;
}

/* 对话框样式 */
:deep(.el-dialog) {
  border-radius: 12px;
}

:deep(.el-dialog__header) {
  padding: 24px 24px 16px 24px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 24px;
}

:deep(.el-dialog__footer) {
  padding: 16px 24px 24px 24px;
  border-top: 1px solid #ebeef5;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 图片上传样式 */
.image-upload-section {
  width: 100%;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 200px;
  height: 200px;
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.3s;
  cursor: pointer;
}

.upload-placeholder:hover {
  border-color: #409EFF;
  background: #f0f9ff;
}

.upload-icon {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.upload-text {
  text-align: center;
  color: #606266;
}

.upload-text p {
  margin: 4px 0;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
}

.uploaded-image {
  position: relative;
  display: inline-block;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
  border-radius: 8px;
}

.uploaded-image:hover .image-overlay {
  opacity: 1;
}

/* 表单样式 */
:deep(.el-form-item__label) {
  font-weight: 600;
  color: #606266;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-input__wrapper:hover) {
  border-color: #409EFF;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-textarea__inner) {
  border-radius: 8px;
}

/* 标签样式 */
:deep(.el-tag) {
  border-radius: 6px;
  font-weight: 500;
}

/* 按钮样式 */
:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);
  border: none;
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #3a8ee6 0%, #337ecc 100%);
}

:deep(.el-button--success) {
  background: linear-gradient(135deg, #67c23a 0%, #5daf34 100%);
  border: none;
}

:deep(.el-button--success:hover) {
  background: linear-gradient(135deg, #5daf34 0%, #529b2e 100%);
}

:deep(.el-button--warning) {
  background: linear-gradient(135deg, #e6a23c 0%, #d19e36 100%);
  border: none;
}

:deep(.el-button--warning:hover) {
  background: linear-gradient(135deg, #d19e36 0%, #b88230 100%);
}

:deep(.el-button--danger) {
  background: linear-gradient(135deg, #f56c6c 0%, #f45656 100%);
  border: none;
}

:deep(.el-button--danger:hover) {
  background: linear-gradient(135deg, #f45656 0%, #e54545 100%);
}

/* 加载状态样式 */
:deep(.el-loading-mask) {
  border-radius: 12px;
}

/* 空状态样式 */
:deep(.el-table__empty-text) {
  color: #909399;
  font-size: 14px;
  padding: 60px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .product-management {
    padding: 12px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    padding: 16px;
  }

  .header-right {
    justify-content: flex-start;
  }

  .search-section {
    padding: 16px;
  }

  :deep(.el-table .el-button) {
    padding: 6px 10px;
    font-size: 12px;
    margin: 1px;
  }

  :deep(.el-dialog) {
    width: 95%;
    margin: 5vh auto;
  }

  .upload-placeholder {
    width: 150px;
    height: 150px;
  }
}

@media (max-width: 480px) {
  .header-left h2 {
    font-size: 20px;
  }

  .header-desc {
    font-size: 13px;
  }

  .header-right {
    flex-direction: column;
  }

  :deep(.el-table .el-table__cell) {
    padding: 12px 8px;
  }

  .product-name {
    gap: 4px;
  }

  .name-text {
    font-size: 14px;
  }

  /* 库存相关样式 */
  .stock-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
  }

  .stock-tag {
    font-weight: bold;
  }

  .stock-status {
    font-size: 12px;
    color: #666;
  }

  .stock-management {
    padding: 20px 0;
  }

  .product-info {
    background: #f5f7fa;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
  }

  .product-info h4 {
    margin: 0 0 8px 0;
    color: #303133;
    font-size: 16px;
  }

  .product-info p {
    margin: 0;
    color: #909399;
    font-size: 14px;
  }

  .stock-status-text {
    margin-left: 10px;
    font-size: 12px;
    color: #909399;
  }
}
</style>
