package qidian.it.springboot.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import qidian.it.springboot.config.FileUploadProperties;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 文件上传控制器
 */
@RestController
@RequestMapping("/api/upload")
public class FileUploadController {

    @Autowired
    private FileUploadProperties fileUploadProperties;

    /**
     * 上传商品图片
     */
    @PostMapping("/product-image")
    public Map<String, Object> uploadProductImage(@RequestParam("file") MultipartFile file) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 验证文件是否为空
            if (file.isEmpty()) {
                result.put("success", false);
                result.put("message", "请选择要上传的文件");
                return result;
            }
            
            // 验证文件大小
            if (file.getSize() > fileUploadProperties.getMaxSize()) {
                result.put("success", false);
                result.put("message", "文件大小不能超过" + (fileUploadProperties.getMaxSize() / 1024 / 1024) + "MB");
                return result;
            }
            
            // 获取原始文件名
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.isEmpty()) {
                result.put("success", false);
                result.put("message", "文件名不能为空");
                return result;
            }
            
            // 验证文件格式
            String fileExtension = getFileExtension(originalFilename);
            if (!isAllowedExtension(fileExtension)) {
                result.put("success", false);
                result.put("message", "只支持 " + fileUploadProperties.getAllowedExtensions() + " 格式的图片");
                return result;
            }
            
            // 创建上传目录
            String uploadPath = fileUploadProperties.getPathWithSeparator();
            File uploadDir = new File(uploadPath);
            if (!uploadDir.exists()) {
                boolean created = uploadDir.mkdirs();
                if (!created) {
                    result.put("success", false);
                    result.put("message", "创建上传目录失败");
                    return result;
                }
                System.out.println("创建上传目录: " + uploadPath);
            }

            // 生成唯一文件名
            String fileName = generateUniqueFileName(fileExtension);

            // 保存文件
            Path filePath = Paths.get(uploadPath + fileName);
            Files.copy(file.getInputStream(), filePath);

            // 生成访问URL
            String imageUrl = fileUploadProperties.getUrlPrefix() + "/" + fileName;
            
            System.out.println("图片上传成功:");
            System.out.println("原始文件名: " + originalFilename);
            System.out.println("保存文件名: " + fileName);
            System.out.println("访问URL: " + imageUrl);
            
            result.put("success", true);
            result.put("message", "图片上传成功");
            result.put("data", Map.of(
                "fileName", fileName,
                "originalName", originalFilename,
                "imageUrl", imageUrl,
                "fileSize", file.getSize()
            ));
            
        } catch (IOException e) {
            System.err.println("文件上传失败: " + e.getMessage());
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "文件上传失败: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("上传过程中发生错误: " + e.getMessage());
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "上传失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 删除商品图片
     */
    @DeleteMapping("/product-image")
    public Map<String, Object> deleteProductImage(@RequestParam("fileName") String fileName) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (fileName == null || fileName.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "文件名不能为空");
                return result;
            }
            
            // 安全检查：防止路径遍历攻击
            if (fileName.contains("..") || fileName.contains("/") || fileName.contains("\\")) {
                result.put("success", false);
                result.put("message", "文件名包含非法字符");
                return result;
            }
            
            Path filePath = Paths.get(fileUploadProperties.getPathWithSeparator() + fileName);
            
            if (Files.exists(filePath)) {
                Files.delete(filePath);
                System.out.println("图片删除成功: " + fileName);
                result.put("success", true);
                result.put("message", "图片删除成功");
            } else {
                result.put("success", false);
                result.put("message", "文件不存在");
            }
            
        } catch (IOException e) {
            System.err.println("文件删除失败: " + e.getMessage());
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "文件删除失败: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("删除过程中发生错误: " + e.getMessage());
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "删除失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return "";
        }
        return fileName.substring(lastDotIndex).toLowerCase();
    }
    
    /**
     * 检查文件扩展名是否被允许
     */
    private boolean isAllowedExtension(String extension) {
        String[] allowedExtensions = fileUploadProperties.getAllowedExtensionsArray();
        for (String allowedExt : allowedExtensions) {
            if (allowedExt.trim().equals(extension)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 生成唯一文件名
     */
    private String generateUniqueFileName(String extension) {
        return UUID.randomUUID().toString().replace("-", "") + extension;
    }
}
