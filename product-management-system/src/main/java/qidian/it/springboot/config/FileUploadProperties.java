package qidian.it.springboot.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 文件上传配置属性类
 * 从application.yml中读取文件上传相关配置
 */
@Component
@ConfigurationProperties(prefix = "file.upload")
public class FileUploadProperties {
    
    /**
     * 图片存储路径
     */
    private String path = "/opt/pms/uploads/product_image/";
    
    /**
     * 图片访问URL前缀
     */
    private String urlPrefix = "/product_image";
    
    /**
     * 允许的文件格式
     */
    private String allowedExtensions = ".jpg,.jpeg,.png,.gif,.bmp";
    
    /**
     * 最大文件大小 (字节)
     */
    private long maxSize = 5242880; // 5MB
    
    public String getPath() {
        return path;
    }
    
    public void setPath(String path) {
        this.path = path;
    }
    
    public String getUrlPrefix() {
        return urlPrefix;
    }
    
    public void setUrlPrefix(String urlPrefix) {
        this.urlPrefix = urlPrefix;
    }
    
    public String getAllowedExtensions() {
        return allowedExtensions;
    }
    
    public void setAllowedExtensions(String allowedExtensions) {
        this.allowedExtensions = allowedExtensions;
    }
    
    public long getMaxSize() {
        return maxSize;
    }
    
    public void setMaxSize(long maxSize) {
        this.maxSize = maxSize;
    }
    
    /**
     * 获取允许的文件扩展名数组
     */
    public String[] getAllowedExtensionsArray() {
        return allowedExtensions.split(",");
    }
    
    /**
     * 确保路径以/结尾
     */
    public String getPathWithSeparator() {
        if (path.endsWith("/")) {
            return path;
        }
        return path + "/";
    }
    
    @Override
    public String toString() {
        return "FileUploadProperties{" +
                "path='" + path + '\'' +
                ", urlPrefix='" + urlPrefix + '\'' +
                ", allowedExtensions='" + allowedExtensions + '\'' +
                ", maxSize=" + maxSize +
                '}';
    }
}
