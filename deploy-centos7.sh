#!/bin/bash

# 商品管理系统 CentOS7 部署脚本
# 使用方法: chmod +x deploy-centos7.sh && ./deploy-centos7.sh

echo "=========================================="
echo "商品管理系统 CentOS7 部署脚本"
echo "=========================================="

# 设置变量
PROJECT_DIR="/opt/pms"
UPLOAD_DIR="/opt/pms/uploads/product_image"
LOG_DIR="/opt/pms/logs"
BACKUP_DIR="/opt/pms/backup"
JAR_NAME="product-management-system-1.0-SNAPSHOT.jar"

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "请使用root用户运行此脚本"
    exit 1
fi

echo "1. 创建项目目录结构..."
mkdir -p $PROJECT_DIR
mkdir -p $UPLOAD_DIR
mkdir -p $LOG_DIR
mkdir -p $BACKUP_DIR

echo "2. 设置目录权限..."
chmod 755 $PROJECT_DIR
chmod 755 $UPLOAD_DIR
chmod 755 $LOG_DIR
chmod 755 $BACKUP_DIR

echo "3. 检查Java环境..."
if ! command -v java &> /dev/null; then
    echo "Java未安装，正在安装OpenJDK 11..."
    yum update -y
    yum install -y java-11-openjdk java-11-openjdk-devel
else
    echo "Java已安装: $(java -version 2>&1 | head -n 1)"
fi

echo "4. 检查MySQL环境..."
if ! command -v mysql &> /dev/null; then
    echo "MySQL未安装，请先安装MySQL 8.0"
    echo "安装命令参考:"
    echo "wget https://dev.mysql.com/get/mysql80-community-release-el7-3.noarch.rpm"
    echo "rpm -ivh mysql80-community-release-el7-3.noarch.rpm"
    echo "yum install -y mysql-server"
    echo "systemctl start mysqld"
    echo "systemctl enable mysqld"
    exit 1
else
    echo "MySQL已安装: $(mysql --version)"
fi

echo "5. 检查Node.js环境..."
if ! command -v node &> /dev/null; then
    echo "Node.js未安装，正在安装Node.js 16..."
    curl -fsSL https://rpm.nodesource.com/setup_16.x | bash -
    yum install -y nodejs
else
    echo "Node.js已安装: $(node --version)"
fi

echo "6. 创建systemd服务文件..."
cat > /etc/systemd/system/pms-backend.service << EOF
[Unit]
Description=Product Management System Backend
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=$PROJECT_DIR
ExecStart=/usr/bin/java -jar -Dspring.profiles.active=prod $PROJECT_DIR/$JAR_NAME
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

echo "7. 创建防火墙规则..."
if command -v firewall-cmd &> /dev/null; then
    firewall-cmd --permanent --add-port=8082/tcp
    firewall-cmd --permanent --add-port=80/tcp
    firewall-cmd --permanent --add-port=443/tcp
    firewall-cmd --reload
    echo "防火墙规则已添加"
else
    echo "firewalld未安装，请手动配置防火墙开放8082端口"
fi

echo "8. 创建nginx配置文件..."
if command -v nginx &> /dev/null; then
    cat > /etc/nginx/conf.d/pms.conf << EOF
server {
    listen 80;
    server_name *************;  # 云主机IP地址

    # 前端静态文件
    location / {
        root /opt/pms/frontend/dist;
        try_files \$uri \$uri/ /index.html;
        index index.html;
    }

    # 后端API代理
    location /api/ {
        proxy_pass http://localhost:8082/api/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # 商品图片静态资源
    location /product_image/ {
        proxy_pass http://localhost:8082/product_image/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    }
}
EOF
    echo "Nginx配置文件已创建: /etc/nginx/conf.d/pms.conf"
else
    echo "Nginx未安装，跳过nginx配置"
fi

echo "9. 创建数据库初始化脚本..."
cat > $PROJECT_DIR/init-database.sql << EOF
-- 创建数据库
CREATE DATABASE IF NOT EXISTS pms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE pms;

-- 创建用户表
CREATE TABLE IF NOT EXISTS user (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    role TINYINT DEFAULT 0 COMMENT '角色：0普通用户，1管理员',
    status TINYINT DEFAULT 1 COMMENT '状态：1正常，0禁用',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '用户表';

-- 创建商品分类表
CREATE TABLE IF NOT EXISTS category (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分类ID',
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '商品分类表';

-- 创建商品表
CREATE TABLE IF NOT EXISTS product (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '商品ID',
    name VARCHAR(200) NOT NULL COMMENT '商品名称',
    category_id BIGINT NOT NULL COMMENT '分类ID',
    price DECIMAL(10,2) NOT NULL COMMENT '价格',
    description TEXT COMMENT '商品描述',
    image_url VARCHAR(500) COMMENT '商品图片地址',
    status TINYINT DEFAULT 1 COMMENT '状态：1上架，0下架',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (category_id) REFERENCES category(id)
) COMMENT '商品表';

-- 创建商品库存表
CREATE TABLE IF NOT EXISTS stock (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '库存ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    quantity INT NOT NULL DEFAULT 0 COMMENT '库存数量',
    min_stock INT DEFAULT 10 COMMENT '最低库存预警',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (product_id) REFERENCES product(id)
) COMMENT '商品库存表';

-- 创建购物车表
CREATE TABLE IF NOT EXISTS cart (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '购物车ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    quantity INT NOT NULL DEFAULT 1 COMMENT '数量',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES user(id),
    FOREIGN KEY (product_id) REFERENCES product(id)
) COMMENT '购物车表';

-- 创建订单表
CREATE TABLE IF NOT EXISTS orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '订单ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    order_no VARCHAR(50) NOT NULL UNIQUE COMMENT '订单号',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '订单总金额',
    status TINYINT DEFAULT 0 COMMENT '订单状态：0待支付，1已支付，2已发货，3已完成，4已取消',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES user(id)
) COMMENT '订单表';

-- 创建订单项表
CREATE TABLE IF NOT EXISTS order_item (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '订单项ID',
    order_id BIGINT NOT NULL COMMENT '订单ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    quantity INT NOT NULL COMMENT '数量',
    price DECIMAL(10,2) NOT NULL COMMENT '单价',
    total_price DECIMAL(10,2) NOT NULL COMMENT '小计',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (order_id) REFERENCES orders(id),
    FOREIGN KEY (product_id) REFERENCES product(id)
) COMMENT '订单项表';

-- 创建邮箱验证表
CREATE TABLE IF NOT EXISTS email_verification (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    email VARCHAR(100) NOT NULL COMMENT '邮箱',
    code VARCHAR(10) NOT NULL COMMENT '验证码',
    type TINYINT NOT NULL COMMENT '类型：1注册，2找回密码',
    expire_time TIMESTAMP NOT NULL COMMENT '过期时间',
    used TINYINT DEFAULT 0 COMMENT '是否已使用：0未使用，1已使用',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) COMMENT '邮箱验证表';
EOF

echo "=========================================="
echo "部署脚本执行完成！"
echo "=========================================="
echo "接下来的手动步骤："
echo "1. 将后端jar包复制到: $PROJECT_DIR/$JAR_NAME"
echo "2. 将前端dist目录复制到: /opt/pms/frontend/"
echo "3. 修改前端代码中的 'your-server-ip' 为您的实际IP地址"
echo "4. 执行数据库初始化: mysql -u root -p < $PROJECT_DIR/init-database.sql"
echo "5. 启动后端服务: systemctl start pms-backend"
echo "6. 设置开机自启: systemctl enable pms-backend"
echo "7. 启动nginx: systemctl start nginx && systemctl enable nginx"
echo "8. 检查服务状态: systemctl status pms-backend"
echo "=========================================="
echo "访问地址: http://*************"
echo "后端API: http://*************:8082"
echo "图片上传目录: $UPLOAD_DIR"
echo "日志目录: $LOG_DIR"
echo "=========================================="
